# Dual-Mode Data Loading Guide

The Retrieval MLM configurable training script now supports two distinct data loading modes, allowing you to choose between pre-tokenized data and on-the-fly tokenization based on your needs.

## Overview

| Feature | Pre-tokenized Mode | On-the-fly Mode |
|---------|-------------------|-----------------|
| **Data Format** | .npy files with token IDs | Arrow/Parquet files with raw text |
| **Performance** | Fastest (no tokenization overhead) | Slightly slower (real-time tokenization) |
| **Storage** | Requires pre-processing step | Uses raw text directly |
| **Flexibility** | Fixed tokenization | Can change tokenizer without re-processing |
| **Best For** | Production training, large datasets | Experimentation, smaller datasets |

## Configuration

### Pre-tokenized Mode (Default)
```yaml
data:
  tokenize_on_the_fly: false
  train_data_pattern: "/path/to/train_*.npy"
  val_data_pattern: "/path/to/val_*.npy"
  tokenizer_name: "gpt2"  # For reference only
  mask_prob: 0.3
```

### On-the-fly Tokenization Mode
```yaml
data:
  tokenize_on_the_fly: true
  arrow_data_paths:
    - "/path/to/dataset1"
    - "/path/to/dataset2"
  target_tokens: 20000000000
  tokenizer_name: "gpt2"  # Used for actual tokenization
  mask_prob: 0.3
```

## Usage Examples

### Switching Between Modes

**Current setup (Pre-tokenized):**
```bash
python retrieval_mlm_configurable.py --config config.yaml
```

**Switch to Arrow mode:**
```yaml
# In your config file, change:
data:
  tokenize_on_the_fly: true
  arrow_data_paths:
    - "/s2_nfs/pile/OpenWebText2"
```

```bash
python retrieval_mlm_configurable.py --config config.yaml
```

### Multi-GPU Training (Both Modes)
```bash
# Works with both modes
torchrun --standalone --nproc_per_node=8 retrieval_mlm_configurable.py --config config.yaml
```

## Key Benefits

✅ **Unified Interface**: Same training script handles both data formats
✅ **DDP Compatible**: Both modes work seamlessly with distributed training
✅ **Flexible Experimentation**: Switch between modes with config changes only
✅ **Performance Options**: Choose speed (NPY) vs flexibility (Arrow)
✅ **Backward Compatible**: Existing NPY workflows continue to work

Your current config uses NPY mode with your custom tokenizer path. To experiment with Arrow mode, simply change `tokenize_on_the_fly: true` and specify your Arrow data paths.
