import os
import math
import time
import inspect
import json
from dataclasses import dataclass
import torch
import torch.nn as nn
from torch.nn import functional as F
import numpy as np
import glob
import random
from pathlib import Path

# Remove Kimi tokenizer import and use GPT2 tokenizer
import tiktoken

# Dataset loading support
try:
    from datasets import load_dataset
except ImportError:
    print("Warning: datasets library not found. Please install with: pip install datasets")
    load_dataset = None

class TimeMixedSelfAttention(nn.Module):

    def __init__(self, config):
        super().__init__()
        assert config.n_embd % config.n_head == 0
        
        # Store config parameters
        self.n_head = config.n_head
        self.n_head_4 = config.n_head_4
        self.n_embd = config.n_embd
        self.block_size = config.block_size
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.dtype = torch.bfloat16
        
        # Time-mixing parameters (adapted from gptrwkv-2)
        self.layer_id = getattr(config, 'layer_id', 0)  # Will be set per layer
        self.n_layer = config.n_layer

        # Standard RoPE setup
        head_dim = self.n_embd // self.n_head
        inv_freq = 1.0 / (10000.0 ** (torch.arange(0, head_dim, 2).float() / head_dim))
        self.register_buffer('inv_freq', inv_freq)
        
        # Time mixing parameters initialization
        with torch.no_grad():
            ratio_0_to_1 = self.layer_id / (self.n_layer - 1) if self.n_layer > 1 else 0
            ratio_1_to_almost0 = 1.0 - (self.layer_id / self.n_layer) if self.n_layer > 0 else 1.0         
            ddd = torch.ones(1, 1, self.n_embd)
            for i in range(self.n_embd):
                ddd[0, 0, i] = i / self.n_embd

            self.time_maa_x = nn.Parameter(1.0 - torch.pow(ddd, 0.6 * ratio_1_to_almost0 ** 0.9))
            self.time_maa_q = nn.Parameter(1.0 - torch.pow(ddd, 0.9 * ratio_1_to_almost0)) 
            self.time_maa_k = nn.Parameter(1.0 - (torch.pow(ddd, 0.9 * ratio_1_to_almost0) + 0.4 * ratio_0_to_1))
            self.time_maa_v = nn.Parameter(1.0 - (torch.pow(ddd, 0.4 * ratio_1_to_almost0) + 0.6 * ratio_0_to_1))

            D_MIX_LORA = 28 
            self.time_maa_w1 = nn.Parameter(torch.zeros(self.n_embd, D_MIX_LORA * 3))
            self.time_maa_w2 = nn.Parameter(self.ortho_init(torch.zeros(3, D_MIX_LORA, self.n_embd), 0.1))

        self.time_shift = nn.ZeroPad2d((0, 0, 1, -1))
        
        # Linear projections
        self.query = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.key = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.value = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.c_proj = nn.Linear(self.n_embd, self.n_embd, bias=False)
        self.c_proj.NANOGPT_SCALE_INIT = 1

    def ortho_init(self, x: torch.Tensor, scale: float) -> torch.Tensor:
        with torch.no_grad():
            shape = x.shape
            if len(shape) == 2:
                gain = math.sqrt(max(1.0, shape[0] / shape[1]))
                nn.init.orthogonal_(x, gain=gain * scale)
            elif len(shape) == 3:
                gain = math.sqrt(max(1.0, shape[1] / shape[2]))
                for i in range(shape[0]):
                    nn.init.orthogonal_(x[i], gain=gain * scale)
            else:
                raise ValueError("Unsupported tensor shape for ortho_init.")
            return x

    def rotate_half(self, x):
        """Rotates half the hidden dims of the input."""
        x1, x2 = x.chunk(2, dim=-1)
        return torch.cat((-x2, x1), dim=-1)

    def apply_rotary_pos_emb(self, q, k, cos, sin):
        """Apply rotary position embeddings to q and k tensors"""
        q_embed = (q * cos) + (self.rotate_half(q) * sin)
        k_embed = (k * cos) + (self.rotate_half(k) * sin)
        return q_embed, k_embed

    def forward(self, x, attention_mask=None):
        B, T, C = x.size()
        H = self.n_head
        head_dim = C // H
        
        # Time-mixing logic (bidirectional): use average of neighbors (x(t-1)+x(t+1))/2
        x_left = torch.zeros_like(x)
        x_left[:, 1:, :] = x[:, :-1, :]
        x_right = torch.zeros_like(x)
        x_right[:, :-1, :] = x[:, 1:, :]
        if attention_mask is not None:
            m = attention_mask.to(dtype=x.dtype).unsqueeze(-1)
            left_mask = torch.zeros_like(m)
            left_mask[:, 1:, :] = m[:, :-1, :]
            right_mask = torch.zeros_like(m)
            right_mask[:, :-1, :] = m[:, 1:, :]
            numer = x_left * left_mask + x_right * right_mask
            denom = left_mask + right_mask
            x_neighbors = torch.where(denom > 0, numer / torch.clamp(denom, min=1.0), x)
        else:
            x_neighbors = 0.5 * (x_left + x_right)
        xx = x_neighbors - x
        x_base = x + xx * self.time_maa_x
        lora_pre_bmm = torch.tanh(x_base @ self.time_maa_w1).view(B * T, 3, -1).transpose(0, 1)
        lora_components = torch.bmm(lora_pre_bmm, self.time_maa_w2).view(3, B, T, C)
        lora_q, lora_k, lora_v = lora_components.unbind(dim=0)
        
        xq_intermediate = x + xx * (self.time_maa_q + lora_q)
        xk_intermediate = x + xx * (self.time_maa_k + lora_k)
        xv_intermediate = x + xx * (self.time_maa_v + lora_v)

        # Projections
        q = self.query(xq_intermediate).view(B, T, H, head_dim).transpose(1, 2)
        k = self.key(xk_intermediate).view(B, T, H, head_dim).transpose(1, 2)
        v = self.value(xv_intermediate).view(B, T, H, head_dim).transpose(1, 2)

        # Standard RoPE position embeddings
        seq_len = T
        position_ids = torch.arange(seq_len, device=x.device, dtype=torch.long).unsqueeze(0)
        freqs = torch.outer(position_ids.float().squeeze(), self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        cos = torch.cos(emb).unsqueeze(0).unsqueeze(0)  # (1, 1, seq_len, head_dim)
        sin = torch.sin(emb).unsqueeze(0).unsqueeze(0)  # (1, 1, seq_len, head_dim)
        # Normalize q and k along the head_dim to stabilize attention
        q, k = F.normalize(q, dim=-1), F.normalize(k, dim=-1)
        # Apply rotary embeddings
        q, k = self.apply_rotary_pos_emb(q, k, cos, sin)

        # Attention computation (bidirectional). Apply padding mask if provided.
        attn_mask = None
        if attention_mask is not None:
            # attention_mask: (B, T) with 1 for tokens to attend, 0 for PAD
            # We want to mask out PAD keys so queries cannot attend to them.
            key_pad = (~attention_mask.bool())  # True where PAD
            # Build additive mask with -inf on PAD keys, shape broadcastable to (B, H, T, T)
            additive = key_pad[:, None, None, :].to(dtype=torch.float32)
            additive = additive.masked_fill(additive > 0, float('-inf')).masked_fill(additive == 0, 0.0)
            attn_mask = additive
        y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_mask, is_causal=False)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        
        # Output projection
        y = self.c_proj(y)

        return y

class EncoderMLP(nn.Module):

    def __init__(self, config):
        super().__init__()
        
        # SwiGLU approach: gate and value with expanded intermediate size for SwiGLU
        # SwiGLU typically uses 8/3 * hidden_dim to maintain parameter count
        self.hidden_dim = int(8 * config.n_embd / 3)
        
        # SwiGLU projections: gate (gets swish) and value
        self.c_fc_gate = nn.Linear(config.n_embd, self.hidden_dim, bias=False)
        self.c_fc_value = nn.Linear(config.n_embd, self.hidden_dim, bias=False)
        self.c_proj = nn.Linear(self.hidden_dim, config.n_embd, bias=False)
        self.c_proj.NANOGPT_SCALE_INIT = 1

        # Time-mixing parameters
        self.layer_id = getattr(config, 'layer_id', 0)  # Will be set per layer
        self.n_layer = config.n_layer
        self.n_embd = config.n_embd
        self.time_shift = nn.ZeroPad2d((0, 0, 1, -1))

        with torch.no_grad():
            ratio_0_to_1 = self.layer_id / (self.n_layer - 1) if self.n_layer > 1 else 0
            ratio_1_to_almost0 = 1.0 - (self.layer_id / self.n_layer) if self.n_layer > 0 else 1.0
            ddd = torch.ones(1, 1, self.n_embd)
            for i in range(self.n_embd):
                ddd[0, 0, i] = i / self.n_embd
            
            # Base time mixing parameter
            self.time_maa_x = nn.Parameter(1.0 - torch.pow(ddd, 0.6 * ratio_1_to_almost0 ** 0.9))
            
            # Separate time mixing parameters for gate and value (SwiGLU style)
            self.time_maa_gate = nn.Parameter(1.0 - torch.pow(ddd, 0.3 * ratio_1_to_almost0))
            self.time_maa_value = nn.Parameter(1.0 - torch.pow(ddd, 0.5 * ratio_1_to_almost0))

            # LoRA components for gate and value
            D_MIX_LORA = 32
            self.time_maa_w1 = nn.Parameter(torch.zeros(self.n_embd, D_MIX_LORA * 2))
            self.time_maa_w2 = nn.Parameter(self.ortho_init(torch.zeros(2, D_MIX_LORA, self.n_embd), 0.1))

    def ortho_init(self, x: torch.Tensor, scale: float) -> torch.Tensor:
        with torch.no_grad():
            shape = x.shape
            if len(shape) == 2:
                gain = math.sqrt(max(1.0, shape[0] / shape[1]))
                nn.init.orthogonal_(x, gain=gain * scale)
            elif len(shape) == 3:
                gain = math.sqrt(max(1.0, shape[1] / shape[2]))
                for i in range(shape[0]):
                    nn.init.orthogonal_(x[i], gain=gain * scale)
            else:
                raise ValueError("Unsupported tensor shape for ortho_init.")
            return x

    def swish(self, x):
        """Swish activation: x * sigmoid(x)"""
        return x * torch.sigmoid(x)

    def forward(self, x, attention_mask=None):
        B, T, C = x.shape
        
        # RWKV time-mixing logic with SwiGLU (bidirectional neighbors average)
        x_left = torch.zeros_like(x)
        x_left[:, 1:, :] = x[:, :-1, :]
        x_right = torch.zeros_like(x)
        x_right[:, :-1, :] = x[:, 1:, :]
        if attention_mask is not None:
            m = attention_mask.to(dtype=x.dtype).unsqueeze(-1)
            left_mask = torch.zeros_like(m)
            left_mask[:, 1:, :] = m[:, :-1, :]
            right_mask = torch.zeros_like(m)
            right_mask[:, :-1, :] = m[:, 1:, :]
            numer = x_left * left_mask + x_right * right_mask
            denom = left_mask + right_mask
            x_neighbors = torch.where(denom > 0, numer / torch.clamp(denom, min=1.0), x)
        else:
            x_neighbors = 0.5 * (x_left + x_right)
        xx = x_neighbors - x
        x_base = x + xx * self.time_maa_x
        
        # LoRA components for gate and value
        lora_pre_bmm = torch.tanh(x_base @ self.time_maa_w1).view(B * T, 2, -1).transpose(0, 1)
        lora_components = torch.bmm(lora_pre_bmm, self.time_maa_w2).view(2, B, T, C)
        lora_gate, lora_value = lora_components.unbind(dim=0)
        
        # Separate x_intermediate for gate and value (SwiGLU style)
        x_gate = x + xx * (self.time_maa_gate + lora_gate)
        x_value = x + xx * (self.time_maa_value + lora_value)
        
        # SwiGLU: Swish(gate) * value
        gate = self.c_fc_gate(x_gate)      # gate projection
        value = self.c_fc_value(x_value)   # value projection
        
        # Apply SwiGLU: Swish(gate) ⊙ value
        y = self.swish(gate) * value
        
        # Project back to original dimension
        y = self.c_proj(y)
        
        return y

class EncoderBlock(nn.Module):

    def __init__(self, config, layer_id):
        super().__init__()
        self.ln_1 = nn.LayerNorm(config.n_embd)
        
        # Create a config copy with layer_id for this specific layer
        layer_config = config
        layer_config.layer_id = layer_id
        
        self.attn = TimeMixedSelfAttention(layer_config)
        self.ln_2 = nn.LayerNorm(config.n_embd)
        self.mlp = EncoderMLP(layer_config)

    def forward(self, x, attention_mask=None):
        x = x + self.attn(self.ln_1(x), attention_mask=attention_mask)
        x = x + self.mlp(self.ln_2(x), attention_mask=attention_mask)
        return x

@dataclass
class EncoderConfig:
    block_size: int = 1024 # max sequence length
    vocab_size: int = 50257 # base GPT2 vocab size (we will add special tokens)
    n_layer: int = 12 # number of layers
    n_head: int = 12 # number of heads
    n_embd: int = 768 # embedding dimension
    n_head_4: int = 12 # embedding dimension
    mask_prob: float = 0.2 # probability of masking tokens for MLM

class MaskedLanguageModel(nn.Module):
    """Bidirectional Transformer Encoder for Masked Language Modeling with weight tying"""

    def __init__(self, config):
        super().__init__()
        self.config = config

        # Add special tokens: [MASK], [PAD], [CLS], [EOS]
        self.mask_token_id = config.vocab_size      # id for [MASK]
        self.pad_token_id = config.vocab_size + 1   # id for [PAD]
        self.cls_token_id = config.vocab_size + 2   # id for [CLS]
        self.eos_token_id = config.vocab_size + 3   # id for [EOS]
        effective_vocab_size = config.vocab_size + 4

        self.transformer = nn.ModuleDict(dict(
            wte = nn.Embedding(effective_vocab_size, config.n_embd),
            h = nn.ModuleList([EncoderBlock(config, layer_id=i) for i in range(config.n_layer)]),
            ln_f = nn.LayerNorm(config.n_embd),
        ))
        # Gate projection for hidden state-based interpolation (d -> d)
        self.hidden_gate = nn.Linear(config.n_embd, config.n_embd, bias=False)
        # MLM head uses interpolated embeddings (d -> vocab)
        self.lm_head = nn.Linear(config.n_embd, effective_vocab_size, bias=False)

        # init params
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            std = 0.02
            if hasattr(module, 'NANOGPT_SCALE_INIT'):
                std *= (2 * self.config.n_layer) ** -0.5
            torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def forward(self, idx, attention_mask=None, labels=None):
        # idx is of shape (B, T)
        B, T = idx.size()
        assert T <= self.config.block_size, f"Cannot forward sequence of length {T}, block size is only {self.config.block_size}"
        
        # token embeddings
        tok_emb = self.transformer.wte(idx) # (B, T, n_embd)
        
        x = tok_emb
        # forward the blocks of the transformer
        for block in self.transformer.h:
            x = block(x, attention_mask=attention_mask)
        # forward the final layernorm
        x = self.transformer.ln_f(x)
        
        # Extract CLS and EOS token embeddings
        cls = x[:, 0, :]  # (B, d) - CLS token at position 0
        
        # Find EOS token positions (last non-pad token before padding)
        # For now, assume EOS is at a fixed position (T-1) or find it dynamically
        if attention_mask is not None:
            # Find the last non-pad position for each sequence
            seq_lengths = attention_mask.sum(dim=1)  # (B,)
            eos_positions = seq_lengths - 1  # EOS should be at the last valid position
            # Extract EOS tokens using advanced indexing
            batch_indices = torch.arange(B, device=x.device)
            eos = x[batch_indices, eos_positions, :]  # (B, d)
        else:
            # Fallback: assume EOS is at position T-1
            eos = x[:, -1, :]  # (B, d)
        
        # For MLM loss, we only need to compute logits for positions that have labels != -100
        # This means we only interpolate CLS+EOS for masked positions
        if labels is not None:
            # Find positions where labels != -100 (i.e., masked positions)
            mask_positions = (labels != -100)  # (B, T)
            
            if mask_positions.any():
                # Get indices of masked positions
                batch_indices, pos_indices = torch.where(mask_positions)
                
                # Create interpolation weights only for masked positions
                if attention_mask is not None:
                    # Use actual EOS positions for normalization
                    eos_pos_for_masked = eos_positions[batch_indices].float()  # (num_masked,)
                    # Normalize positions: alpha = position / eos_position
                    alpha_masked = pos_indices.float() / torch.clamp(eos_pos_for_masked, min=1.0)  # (num_masked,)
                    alpha_masked = torch.clamp(alpha_masked, 0.0, 1.0)  # (num_masked,)
                else:
                    # Fallback: normalize by T-1
                    alpha_masked = pos_indices.float() / (T - 1) if T > 1 else torch.zeros_like(pos_indices.float())
                
                # Get CLS and EOS for the relevant batches
                cls_for_masked = cls[batch_indices]  # (num_masked, d)
                eos_for_masked = eos[batch_indices]  # (num_masked, d)
                
                # Get hidden states for masked positions
                hidden_for_masked = x[batch_indices, pos_indices, :]  # (num_masked, d)
                
                # Position-wise interpolation only for masked positions
                alpha_expanded = alpha_masked.unsqueeze(-1)  # (num_masked, 1)
                pos_interpolated_masked = (1 - alpha_expanded) * cls_for_masked + alpha_expanded * eos_for_masked  # (num_masked, d)
                
                # Hidden state-based interpolation using gate
                gate_weights = torch.sigmoid(self.hidden_gate(hidden_for_masked))  # (num_masked, d)
                hidden_interpolated_masked = cls_for_masked * gate_weights + eos_for_masked * (1 - gate_weights)  # (num_masked, d)
                
                # Combine both interpolations using mean
                final_interpolated_masked = (pos_interpolated_masked + hidden_interpolated_masked) / 2.0  # (num_masked, d)
                
                # Compute logits only for masked positions
                logits_masked = self.lm_head(final_interpolated_masked)  # (num_masked, vocab_size)
                
                # Create full logits tensor (we only need masked positions for loss)
                logits = torch.zeros(B, T, logits_masked.size(-1), device=x.device, dtype=logits_masked.dtype)
                logits[batch_indices, pos_indices] = logits_masked
            else:
                # No masked positions, create empty logits
                logits = torch.zeros(B, T, self.lm_head.out_features, device=x.device, dtype=x.dtype)
        else:
            # For inference/evaluation, we might need full interpolation (fallback)
            # Position-wise interpolation of CLS and EOS for all positions
            positions = torch.arange(T, device=x.device).float()  # (T,)
            
            if attention_mask is not None:
                # Use actual EOS positions for normalization
                eos_positions_expanded = eos_positions.unsqueeze(1).float()  # (B, 1)
                alpha = positions.unsqueeze(0) / torch.clamp(eos_positions_expanded, min=1.0)  # (B, T)
                alpha = torch.clamp(alpha, 0.0, 1.0)  # (B, T)
                alpha = alpha.unsqueeze(-1)  # (B, T, 1)
            else:
                alpha = positions / (T - 1) if T > 1 else torch.zeros_like(positions)  # (T,)
                alpha = alpha.unsqueeze(0).unsqueeze(-1)  # (1, T, 1)
            
            # Expand CLS and EOS to all positions
            cls_expanded = cls.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
            eos_expanded = eos.unsqueeze(1).expand(-1, T, -1)  # (B, T, d)
            
            # Position-wise interpolation: (1-alpha) * CLS + alpha * EOS
            pos_interpolated = (1 - alpha) * cls_expanded + alpha * eos_expanded  # (B, T, d)
            
            # Hidden state-based interpolation using gate
            gate_weights = torch.sigmoid(self.hidden_gate(x))  # (B, T, d)
            hidden_interpolated = cls_expanded * gate_weights + eos_expanded * (1 - gate_weights)  # (B, T, d)
            
            # Combine both interpolations using mean
            final_interpolated = (pos_interpolated + hidden_interpolated) / 2.0  # (B, T, d)
            
            # MLM head: use combined interpolated embeddings for predictions
            logits = self.lm_head(final_interpolated)  # (B, T, vocab_size)
        
        loss = None
        # MLM loss: labels should be -100 for non-masked positions (and PAD positions)
        if labels is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), labels.view(-1), ignore_index=-100)
        return logits, loss

    def configure_optimizers(self, weight_decay, learning_rate, device_type, embedding_lr_scale=0.1):
        # start with all of the candidate parameters (that require grad)
        param_dict = {pn: p for pn, p in self.named_parameters()}
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        
        # Separate parameters into different groups based on type and learning rate
        embedding_decay_params = []
        embedding_nodecay_params = []
        regular_decay_params = []
        regular_nodecay_params = []
        
        for pn, p in param_dict.items():
            # Check if this is an embedding parameter (specifically CLS/EOS tokens)
            is_embedding = 'transformer.wte' in pn
            
            if p.dim() >= 2:
                if is_embedding:
                    embedding_decay_params.append(p)
                else:
                    regular_decay_params.append(p)
            else:
                if is_embedding:
                    embedding_nodecay_params.append(p)
                else:
                    regular_nodecay_params.append(p)
        
        # Create optimizer groups with different learning rates
        optim_groups = []
        
        # Regular parameters (full learning rate)
        if regular_decay_params:
            optim_groups.append({
                'params': regular_decay_params, 
                'weight_decay': weight_decay,
                'lr': learning_rate
            })
        if regular_nodecay_params:
            optim_groups.append({
                'params': regular_nodecay_params, 
                'weight_decay': 0.0,
                'lr': learning_rate
            })
        
        # Embedding parameters (reduced learning rate)
        embedding_lr = learning_rate * embedding_lr_scale
        if embedding_decay_params:
            optim_groups.append({
                'params': embedding_decay_params, 
                'weight_decay': weight_decay,
                'lr': embedding_lr
            })
        if embedding_nodecay_params:
            optim_groups.append({
                'params': embedding_nodecay_params, 
                'weight_decay': 0.0,
                'lr': embedding_lr
            })
        
        # Print parameter counts
        num_regular_decay = sum(p.numel() for p in regular_decay_params)
        num_regular_nodecay = sum(p.numel() for p in regular_nodecay_params)
        num_embedding_decay = sum(p.numel() for p in embedding_decay_params)
        num_embedding_nodecay = sum(p.numel() for p in embedding_nodecay_params)
        
        print(f"Regular params (lr={learning_rate:.2e}): decay={num_regular_decay:,}, no_decay={num_regular_nodecay:,}")
        print(f"Embedding params (lr={embedding_lr:.2e}): decay={num_embedding_decay:,}, no_decay={num_embedding_nodecay:,}")
        
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == "cuda"
        print(f"using fused AdamW: {use_fused}")
        optimizer = torch.optim.AdamW(optim_groups, betas=(0.9, 0.95), eps=1e-8, fused=use_fused)
        return optimizer 

# -----------------------------------------------------------------------------

class DataLoaderLite:
    """Data loader for OpenWebText dataset in Arrow format without sampling"""
    def __init__(self, B, T, process_rank, num_processes, split, target_tokens=20_000_000_000):
        self.B = B
        self.T = T
        self.process_rank = process_rank
        self.num_processes = num_processes
        self.target_tokens = target_tokens
        assert split in {'train', 'test'}

        # OpenWebText dataset path only
        self.pile_subsets = [
            "/s2_nfs/pile/OpenWebText2"
        ]
        
        # Get all Arrow files from the subsets
        self.data_files = []
        self.subset_files = {}
        
        for subset_path in self.pile_subsets:
            subset_name = Path(subset_path).name
            split_path = Path(subset_path) / split
            
            if split_path.exists():
                arrow_files = list(split_path.glob("*.arrow"))
                if arrow_files:
                    self.subset_files[subset_name] = arrow_files
                    self.data_files.extend(arrow_files)
                    if process_rank == 0:
                        print(f"Found {len(arrow_files)} .arrow files in {subset_name}/{split}")
                else:
                    if process_rank == 0:
                        print(f"Warning: No .arrow files found in {split_path}")
            else:
                if process_rank == 0:
                    print(f"Warning: Path {split_path} does not exist")
        
        assert len(self.data_files) > 0, f"No .arrow files found for split {split}"
        
        if process_rank == 0:
            print(f"Total: {len(self.data_files)} .arrow files for split {split}")
            print(f"Using OpenWebText only - no sampling applied")
        
        # Initialize random seed for consistent sampling across processes
        random.seed(1337 + process_rank)
        
        # Start with first file
        self.current_file_idx = 0
        self.current_shard_data = None
        self.current_position = 0
        self.load_current_file()
        
        # Skip to process-specific position
        self.current_position = B * T * process_rank

    def load_current_file(self):
        """Load current Arrow/Parquet file using datasets library"""
        if load_dataset is None:
            raise ImportError("datasets library is required for loading files")
            
        file_path = self.data_files[self.current_file_idx]
        if self.process_rank == 0:
            print(f"Loading file: {file_path}")
        
        try:
            # Try loading as arrow first, then parquet
            if file_path.suffix == '.arrow':
                dataset_obj = load_dataset('arrow', data_files=str(file_path))['train']
            else:  # assume parquet or other format
                dataset_obj = load_dataset('parquet', data_files=str(file_path))['train']
            
            if self.process_rank == 0:
                print(f"Loaded dataset with {len(dataset_obj)} examples")
                print(f"Available columns: {dataset_obj.column_names}")
            
            # Extract text column (try different possible column names)
            text_column = None
            for col in ['text', 'content', 'article', 'document']:
                if col in dataset_obj.column_names:
                    text_column = col
                    break
            
            if text_column is None:
                raise ValueError(f"No text column found in {file_path}. Available columns: {dataset_obj.column_names}")
            
            if self.process_rank == 0:
                print(f"Using text column '{text_column}'")
            
            # Filter and extract texts with sampling and chunking
            sampled_texts = []
            skipped_short = 0
            skipped_empty = 0
            total_texts = 0
            total_chunks = 0
            
            for example in dataset_obj:
                total_texts += 1
                text = example[text_column]
                
                if not text:
                    skipped_empty += 1
                    continue
                    
                cleaned_text = text.strip()
                if len(cleaned_text) <= 100:  # Filter very short texts
                    skipped_short += 1
                    continue
                
                # No sampling - use all texts
                # For long texts, chunk them into smaller pieces
                # Target chunk size: ~1022 tokens (leaving room for CLS/EOS in 1024 sequence)
                chunks = self.chunk_text(cleaned_text, target_tokens=1022)
                sampled_texts.extend(chunks)
                total_chunks += len(chunks)
            
            # Store raw texts for on-the-fly tokenization
            self.raw_texts = sampled_texts
            self.text_index = 0  # Current position in text list
            self.current_tokens = []  # Buffer for current document tokens
            self.token_position = 0  # Position in current token buffer
            
            # Initialize tokenizer
            if not hasattr(self, 'tokenizer'):
                self.tokenizer = tiktoken.get_encoding("gpt2")
            
            if self.process_rank == 0:
                print(f"Loaded {len(sampled_texts)} text chunks from {total_texts} total documents")
                print(f"Created {total_chunks} chunks from all documents")
                print(f"Skipped {skipped_empty} empty texts, {skipped_short} short texts")
                if len(sampled_texts) > 0:
                    sample_text = sampled_texts[0]
                    print(f"Sample chunk length: {len(sample_text)} chars")
                    print(f"Sample chunk start: {sample_text[:100]}...")
                
        except Exception as e:
            if self.process_rank == 0:
                print(f"Error loading {file_path}: {e}")
                import traceback
                traceback.print_exc()
            
            # Skip to next file on error
            self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
            if self.current_file_idx == 0:  # Wrapped around
                raise RuntimeError("All files failed to load")
            self.load_current_file()
            return
    
    def chunk_text(self, text, target_tokens=800, overlap_tokens=50):
        """Chunk long text into smaller pieces suitable for 1024 context length"""
        if not hasattr(self, 'tokenizer'):
            self.tokenizer = tiktoken.get_encoding("gpt2")
        
        # Tokenize the full text
        tokens = self.tokenizer.encode(text)
        
        # If text is short enough, return as single chunk
        if len(tokens) <= target_tokens:
            return [text]
        
        chunks = []
        start_idx = 0
        
        while start_idx < len(tokens):
            # Extract chunk of target size
            end_idx = min(start_idx + target_tokens, len(tokens))
            chunk_tokens = tokens[start_idx:end_idx]
            
            # Decode back to text
            chunk_text = self.tokenizer.decode(chunk_tokens)
            
            # Clean up chunk boundaries - try to break at sentence/paragraph boundaries
            if end_idx < len(tokens):  # Not the last chunk
                # Look for good break points (sentence endings)
                chunk_text = self.clean_chunk_boundary(chunk_text)
            
            chunks.append(chunk_text.strip())
            
            # Move start position with overlap
            if end_idx >= len(tokens):
                break
            start_idx = end_idx - overlap_tokens
        
        return chunks
    
    def clean_chunk_boundary(self, text):
        """Clean chunk boundaries to break at natural points"""
        # Try to break at sentence endings
        for delimiter in ['. ', '! ', '? ', '\n\n', '\n']:
            if delimiter in text[-200:]:  # Look in last 200 chars
                last_delim = text.rfind(delimiter)
                if last_delim > len(text) * 0.8:  # Only if delimiter is near the end
                    return text[:last_delim + len(delimiter.rstrip())]
        
        # If no good break point found, return as is
        return text
    
    def get_tokens(self, num_tokens):
        """Get specified number of tokens, tokenizing on-the-fly"""
        tokens = []
        
        while len(tokens) < num_tokens:
            # If current token buffer is empty or exhausted, tokenize next text
            if self.token_position >= len(self.current_tokens):
                if self.text_index >= len(self.raw_texts):
                    # Need to load next file
                    self.current_file_idx = (self.current_file_idx + 1) % len(self.data_files)
                    self.load_current_file()
                    if len(self.raw_texts) == 0:
                        break  # No more data
                
                # Tokenize next text
                if self.text_index < len(self.raw_texts):
                    text = self.raw_texts[self.text_index]
                    self.current_tokens = self.tokenizer.encode(text)
                    self.text_index += 1
                    self.token_position = 0
                else:
                    break
            
            # Extract tokens from current buffer
            remaining_needed = num_tokens - len(tokens)
            available_tokens = len(self.current_tokens) - self.token_position
            take_tokens = min(remaining_needed, available_tokens)
            
            if take_tokens > 0:
                tokens.extend(self.current_tokens[self.token_position:self.token_position + take_tokens])
                self.token_position += take_tokens
        
        return tokens

    def next_batch(self):
        """Return inputs, MLM labels, and attention mask.
        - Inputs are sequences of length T with a dedicated [CLS] token at position 0.
        - 30% of tokens are replaced with [MASK] token id.
        - Labels contain original token ids at masked positions and -100 elsewhere.
        """
        B, T = self.B, self.T
        buf = torch.zeros((B, T), dtype=torch.long)

        for i in range(B):
            # We need T-2 tokens from dataset (leaving room for [CLS] and [EOS])
            need = T - 2
            
            # Get tokens using on-the-fly tokenization
            tokens = self.get_tokens(need)
            
            # Handle case where we don't get enough tokens
            if len(tokens) < need:
                # Pad with available tokens or repeat if necessary
                while len(tokens) < need:
                    if len(tokens) == 0:
                        # Emergency fallback - create some dummy tokens
                        tokens = [self.tokenizer.encode("The")[0]] * need
                        break
                    # Repeat existing tokens
                    tokens.extend(tokens[:min(len(tokens), need - len(tokens))])
                tokens = tokens[:need]  # Truncate to exact length
            
            # Create sequence: [CLS] + content_tokens + [EOS] + [PAD]...
            row = torch.full((T,), EncoderConfig.vocab_size + 1, dtype=torch.long)  # Initialize with PAD tokens
            row[0] = EncoderConfig.vocab_size + 2  # [CLS] id
            
            # Place actual content tokens
            actual_tokens = len(tokens)
            row[1:1+actual_tokens] = torch.tensor(tokens[:actual_tokens], dtype=torch.long)
            
            # Place EOS right after actual content tokens
            eos_position = 1 + actual_tokens
            if eos_position < T:  # Make sure we don't exceed sequence length
                row[eos_position] = EncoderConfig.vocab_size + 3  # [EOS] id
            
            # Remaining positions are already PAD tokens from initialization
            buf[i] = row

        # Prepare MLM inputs and labels
        inputs = buf.clone()
        labels = torch.full_like(buf, -100)  # ignore by default

        # 30% mask probability (do not mask PAD, CLS, or EOS tokens)
        mask_prob = 0.3
        pad_token_id = EncoderConfig.vocab_size + 1
        cls_token_id = EncoderConfig.vocab_size + 2
        eos_token_id = EncoderConfig.vocab_size + 3
        rand = torch.rand(buf.shape)
        mask = (rand < mask_prob) & (buf != pad_token_id) & (buf != cls_token_id) & (buf != eos_token_id)
        
        # Ensure at least one masked token per sequence to avoid zero-loss steps (important for DDP)
        for i in range(B):
            if not mask[i].any():
                # candidates: non-special, non-pad positions [1: ] (allow content positions only)
                candidates = (buf[i] != pad_token_id) & (buf[i] != cls_token_id) & (buf[i] != eos_token_id)
                if candidates.any():
                    idxs = torch.nonzero(candidates, as_tuple=False).squeeze(-1)
                    # pick a random candidate to mask
                    pick = idxs[torch.randint(0, idxs.numel(), (1,)).item()]
                    mask[i, pick] = True
        
        labels[mask] = buf[mask]

        # Replace masked positions with [MASK] token id (vocab_size)
        # Note: Mask token id is vocab_size; model adds +3 rows for special tokens
        mask_token_id = EncoderConfig.vocab_size  # static access; same as model's assumption
        inputs[mask] = mask_token_id

        # Ensure PAD tokens are ignored in loss
        labels[inputs == pad_token_id] = -100
        # Ensure CLS tokens are ignored in loss
        labels[inputs == cls_token_id] = -100
        # Ensure EOS tokens are ignored in loss
        labels[inputs == eos_token_id] = -100

        # Attention mask: 1 for real tokens (including CLS), 0 for PAD
        attention_mask = ((inputs != pad_token_id).to(torch.long))

        return inputs, labels, attention_mask

def evaluate_mlm_loss(model, data_loader, device, device_type, num_batches=20):
    model.eval()
    total_loss = 0.0
    total_steps = 0
    with torch.no_grad():
        for _ in range(num_batches):
            x, labels, attention_mask = data_loader.next_batch()
            x, labels, attention_mask = x.to(device), labels.to(device), attention_mask.to(device)
            with torch.autocast(device_type=device_type, dtype=torch.bfloat16):
                _, loss = model(x, attention_mask=attention_mask, labels=labels)
            total_loss += loss.item()
            total_steps += 1
    return total_loss / max(1, total_steps)

# -----------------------------------------------------------------------------
# simple launch:
# python train_gpt2.py
# DDP launch for e.g. 8 GPUs:
# torchrun --standalone --nproc_per_node=8 train_gpt2.py

if __name__ == "__main__":
    # run the training loop
    from torch.distributed import init_process_group, destroy_process_group
    from torch.nn.parallel import DistributedDataParallel as DDP
    import torch.distributed as dist
    import datetime
    from torch.utils.tensorboard import SummaryWriter
    
    writer_dir_path = '/home/<USER>/run_logs'
    run_group_name = 'encoder_mlm_124M_pile_20B'
    run_No = "pile_sampling_20B"
    writer = SummaryWriter(log_dir=f"{writer_dir_path}/{run_group_name}/{run_No}")
    
    # set up DDP (distributed data parallel).
    # torchrun command sets the env variables RANK, LOCAL_RANK, and WORLD_SIZE
    ddp = int(os.environ.get('RANK', -1)) != -1 # is this a ddp run?
    if ddp:
        # use of DDP atm demands CUDA, we set the device appropriately according to rank
        assert torch.cuda.is_available(), "for now i think we need CUDA for DDP"
        init_process_group(backend='nccl')
        ddp_rank = int(os.environ['RANK'])
        ddp_local_rank = int(os.environ['LOCAL_RANK'])
        ddp_world_size = int(os.environ['WORLD_SIZE'])
        device = f'cuda:{ddp_local_rank}'
        torch.cuda.set_device(device)
        master_process = ddp_rank == 0 # this process will do logging, checkpointing etc.
    else:
        # vanilla, non-DDP run
        ddp_rank = 0
        ddp_local_rank = 0
        ddp_world_size = 1
        master_process = True
        # attempt to autodetect device
        device = "cpu"
        if torch.cuda.is_available():
            device = "cuda"
        elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
            device = "mps"
        print(f"using device: {device}")
    
    # added after video, pytorch can be serious about it's device vs. device_type distinction
    device_type = "cuda" if device.startswith("cuda") else "cpu"
    
    torch.manual_seed(1337)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(1337)
    
    # Initialize GPT2 tokenizer (only for potential debugging; not used for generation here)
    gpt2_tokenizer = tiktoken.get_encoding("gpt2")
    
    total_batch_size = 1048576 # 2**20, ~1M, in number of tokens
    B = 32 # micro batch size
    T = 1024 # sequence length
    assert total_batch_size % (B * T * ddp_world_size) == 0, "make sure total_batch_size is divisible by B * T * ddp_world_size"
    grad_accum_steps = total_batch_size // (B * T * ddp_world_size)
    if master_process:
        print(f"total desired batch size: {total_batch_size}")
        print(f"micro batch size: {B}")
        print(f"=> calculated gradient accumulation steps: {grad_accum_steps}")
    
    train_loader = DataLoaderLite(B=B, T=T, process_rank=ddp_rank, num_processes=ddp_world_size, split="train", target_tokens=20_000_000_000)
    val_loader = DataLoaderLite(B=B, T=T, process_rank=ddp_rank, num_processes=ddp_world_size, split="test", target_tokens=20_000_000_000)
    
    torch.set_float32_matmul_precision('high')
    
    # create Encoder MLM model
    config = EncoderConfig()
    model = MaskedLanguageModel(config)
    model.to(device)
    use_compile = True 
    if use_compile:
        model = torch.compile(model)
    if ddp:
        model = DDP(model, device_ids=[ddp_local_rank], find_unused_parameters=True)
    raw_model = model.module if ddp else model # always contains the "raw" unwrapped model
    
    max_lr = 2e-4
    min_lr = max_lr * 0.1
    warmup_steps = 400  # Increased warmup for longer training
    max_steps = 20000  # 20B tokens / 1M batch size = 20K steps
    def get_lr(it):
        # 1) linear warmup for warmup_iters steps
        if it < warmup_steps:
            return max_lr * (it+1) / warmup_steps
        # 2) if it > lr_decay_iters, return min learning rate
        if it > max_steps:
            return min_lr
        # 3) in between, use cosine decay down to min learning rate
        decay_ratio = (it - warmup_steps) / (max_steps - warmup_steps)
        assert 0 <= decay_ratio <= 1
        coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio)) # coeff starts at 1 and goes to 0
        return min_lr + coeff * (max_lr - min_lr)
    
    # optimize!
    optimizer = raw_model.configure_optimizers(weight_decay=0.1, learning_rate=6e-4, device_type=device_type, embedding_lr_scale=0.1)
    
    # create the log directory we will write checkpoints to and log to
    log_dir = "log_encoder_mlm_pile_20B_pure_lower_special_tokens_lr_batchprob_new"
    os.makedirs(log_dir, exist_ok=True)
    from datetime import datetime
    log_file = os.path.join(log_dir, f"log_pile_20B_training.txt")
    with open(log_file, "w") as f: # open for writing to clear the file
        pass
    
    # Count trainable parameters
    num_params = sum(p.numel() for p in raw_model.parameters() if p.requires_grad)
    
    if master_process:
        print(f'### starting training loop of Encoder-MLM on Pile 20B @{datetime.now()}')
        print(f'Total trainable parameters: {num_params:_}')
        print(f'torch.compile: {use_compile}')
        print(f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}")
        print(f"=> Target tokens: 20B; Total steps: {max_steps}; Warmup steps: {warmup_steps}")
        
        writer.add_text('Start of training...', f'### starting Encoder-MLM training on Pile 20B @{datetime.now()}; params:{num_params:_}; torch.compile:{use_compile}; \n')
        writer.add_text('HyperParmeters', f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}; Target_tokens:20B; Steps:{max_steps}")
        
        with open(log_file, "a") as f:
            f.write(f'### starting Encoder-MLM training on Pile 20B @{datetime.now()}; params:{num_params:_}\n')
            f.write(f"=> Batch_size:{B}; Sequence_length:{T}; max_lr:{max_lr}; min_lr:{min_lr}; Target_tokens:20B; Steps:{max_steps}\n")

    for step in range(max_steps):
        t0 = time.time()
        last_step = (step == max_steps - 1)
    
        # once in a while evaluate our validation loss (MLM) - less frequent for longer training
        if step % 500 == 0 or last_step:
            model.eval()
            with torch.no_grad():
                val_loss_accum = 0.0
                val_loss_steps = 20
                for _ in range(val_loss_steps):
                    x, labels, attention_mask = val_loader.next_batch()
                    x, labels, attention_mask = x.to(device), labels.to(device), attention_mask.to(device)
                    with torch.autocast(device_type=device_type, dtype=torch.bfloat16):
                        _, loss = model(x, attention_mask=attention_mask, labels=labels)
                    val_loss_accum += loss.detach()
                # Average the accumulated validation loss
                val_loss_accum = val_loss_accum / val_loss_steps
            if ddp:
                dist.all_reduce(val_loss_accum, op=dist.ReduceOp.AVG)

            if master_process:
                writer.add_scalar("loss/val", val_loss_accum.item(), step)
                print(f"Step {step}: validation loss (MLM): {val_loss_accum.item():.4f}")
                with open(log_file, "a") as f:
                    f.write(f"{step} val_mlm {val_loss_accum.item():.4f}\n")

                torch.cuda.empty_cache()
                if step > 0 and (step in [5000, 10000, 15000] or last_step):  # Checkpoints at specific steps
                    # optionally write model checkpoints
                    checkpoint_path = os.path.join(log_dir, f"model_{step:05d}.pt")
                    checkpoint = {
                        'model': raw_model.state_dict(),
                        'config': raw_model.config,
                        'step': step,
                        'val_loss_mlm': val_loss_accum.item()
                    }
                    torch.save(checkpoint, checkpoint_path)
        # Generation section removed for encoder-only MLM model
    
        # do one step of the optimization
        model.train()
        optimizer.zero_grad()
        loss_accum = 0.0
        for micro_step in range(grad_accum_steps):
            x, labels, attention_mask = train_loader.next_batch()
            x, labels, attention_mask = x.to(device), labels.to(device), attention_mask.to(device)
            # added after video, this field is also used by the forward pass.
            if ddp:
                model.require_backward_grad_sync = (micro_step == grad_accum_steps - 1)
            with torch.autocast(device_type=device_type, dtype=torch.bfloat16):
                _, loss = model(x, attention_mask=attention_mask, labels=labels)

            # we have to scale the loss to account for gradient accumulation,
            # because the gradients just add on each successive backward().
            # addition of gradients corresponds to a SUM in the objective, but
            # instead of a SUM we want MEAN. Scale the loss here so it comes out right
            loss = loss / grad_accum_steps
            loss_accum += loss.detach()
            loss.backward()
            
        if ddp:
            dist.all_reduce(loss_accum, op=dist.ReduceOp.AVG)
        norm = torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        # determine and set the learning rate for this iteration
        lr = get_lr(step)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        optimizer.step()
        if device_type == "cuda":
            torch.cuda.synchronize() # wait for the GPU to finish work
        t1 = time.time()
        dt = t1 - t0 # time difference in seconds
        tokens_processed = train_loader.B * train_loader.T * grad_accum_steps * ddp_world_size
        tokens_per_sec = tokens_processed / dt
        if master_process:
            writer.add_scalar("loss/train", loss_accum.item(), step)
            writer.add_scalar("norm", norm, step)
            writer.add_scalar("learning_rate", lr, step)
            print(f"step {step:5d} | loss: {loss_accum.item():.6f} | lr {lr:.4e} | norm: {norm:.4f} | dt: {dt*1000:.2f}ms | tok/sec: {tokens_per_sec:.2f}...")
            with open(log_file, "a") as f:
                f.write(f"{step} train {loss_accum.item():.6f} | lr {lr:.4e} | norm: {norm:.4f} | dt: {dt*1000:.2f}ms | tok/sec: {tokens_per_sec:.2f}...\n")

    if master_process:
        with open(log_file, "a") as f:
            f.write(f'### end of training loop @{datetime.now()}...\n')
    
    writer.close()
    print(f'### end of training loop @{datetime.now()}\n')
    if ddp:
        destroy_process_group() 