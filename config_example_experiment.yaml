# Example: High Learning Rate Experiment
# Copy from config.yaml and modify specific parameters

# Model Architecture Configuration
model:
  block_size: 1024
  vocab_size: 50257
  n_layer: 12
  n_head: 12
  n_embd: 768
  n_head_4: 12
  mask_prob: 0.3
  time_mixing:
    d_mix_lora_attention: 28
    d_mix_lora_mlp: 32
  dtype: "bfloat16"
  use_compile: true

# Training Configuration - Higher learning rate experiment
training:
  total_batch_size: 1048576
  micro_batch_size: 32
  sequence_length: 1024
  max_lr: 4.0e-4               # 2x higher than default
  min_lr_ratio: 0.1
  warmup_steps: 750
  max_steps: 9500
  base_learning_rate: 6.0e-4
  weight_decay: 0.1
  embedding_lr_scale: 0.1
  beta1: 0.9
  beta2: 0.95
  eps: 1.0e-8
  grad_clip_norm: 1.0
  val_eval_interval: 250
  val_loss_steps: 20
  checkpoint_interval: 5000
  random_seed: 1337

# Data Configuration
data:
  train_data_pattern: "/s2_nfs/tooka_tokenizer/edufineweb_train_*.npy"
  val_data_pattern: "/s2_nfs/tooka_tokenizer/edufineweb_val_*.npy"
  tokenizer_name: "/s2_nfs/tooka_tokenizer"
  mask_prob: 0.3

# Infrastructure Configuration
infrastructure:
  device_type: "cuda"
  log_dir: "/s2_nfs/Retrieval_MLM/logs"
  tensorboard:
    writer_dir_path: "/home/<USER>/run_logs"
    run_group_name: "encoder_mlm_124M_edufineweb"
    run_number: "Removed"

# Weights & Biases Configuration - Unique run name
wandb:
  enabled: true
  project: "retrieval-mlm"
  entity: null
  name: "high_lr_4e4_experiment_001"    # Descriptive run name
  group: "learning_rate_experiments"     # Group related experiments
  tags: ["mlm", "retrieval", "encoder", "edufineweb", "high_lr"]
  notes: "Testing 2x higher learning rate (4e-4) vs baseline (2e-4)"
  log_interval: 1
  log_gradients: false
  log_parameters: false
  log_model: false
  sync_tensorboard: false
  save_code: true
  resume: "allow"

# Advanced Configuration
advanced:
  set_float32_matmul_precision: "high"
  ddp:
    find_unused_parameters: true
    backend: "nccl"
  init:
    std: 0.02
    scale_init_layers: true